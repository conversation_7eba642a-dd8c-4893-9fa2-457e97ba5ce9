// components/Guard.tsx
'use client' // This component will use client-side hooks

import { useAbilityContext } from '@/contexts/ability-context' // Adjust path if needed
// import { useRouter } from 'next/router'; // For Pages Router
import { useAuth } from '@clerk/nextjs'
import { useRouter } from 'next/navigation' // For App Router
import { useEffect, useState } from 'react'
import Forbidden from './403' // Your 403 page/component

interface GuardProps {
  resource: string
  action: string
  resourceAttributes?: Record<string, unknown>
  children: React.ReactNode
  // Optional: You can add a loading fallback prop if you want to customize it
  loadingFallback?: React.ReactNode
  // Optional: If you want to explicitly redirect rather than render a component
  // unauthorizedRedirectPath?: string;
}

function Guard({
  resource,
  action,
  children,
  resourceAttributes,
  loadingFallback = (
    <div className="flex w-full justify-center items-center min-h-screen text-gray-600">
      Loading content...
    </div>
  ),
}: GuardProps) {
  const { isLoaded: clerkLoaded } = useAuth()
  const ability = useAbilityContext()

  // State to track if the permission check has definitively concluded
  const [permissionChecked, setPermissionChecked] = useState(false)
  const [hasPermission, setHasPermission] = useState(false)

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    // Only proceed if Clerk authentication is loaded and Ability is loaded
    if (clerkLoaded && ability !== undefined) {
      console.log('=== PERMISSION DEBUG ===')
      console.log('resourceAttributes:', JSON.stringify(resourceAttributes, null, 2))
      console.log('CASL ability rules:', JSON.stringify(ability.rules, null, 2))
      console.log('Permission check params:', {
        action,
        resource,
        resourceObject: resourceAttributes ? { type: resource, ...resourceAttributes } : resource,
      })

      // Perform the actual permission check using the loaded ability
      // If resourceAttributes exist, pass them to the permission check for more granular control
      const canAccess = resourceAttributes
        ? ability.can(action, { type: resource, ...resourceAttributes })
        : ability.can(action, resource)

      console.log('Permission result:', canAccess)
      console.log('=== END DEBUG ===')
      console.log('')
      setHasPermission(canAccess)
      setPermissionChecked(true) // Conclude check: permission determined
    }
    // If clerkLoaded or ability is not yet ready, the effect will re-run when they are.
  }, [clerkLoaded, ability, action, resource, resourceAttributes])

  // --- Render Logic ---

  // 1. Show loading fallback while authentication or abilities are being loaded
  if (!permissionChecked) {
    return loadingFallback
  }

  // 2. If permission has been checked and denied, render the Forbidden page
  if (!hasPermission) {
    // If you prefer a full URL redirect, you could use:
    // useEffect(() => {
    //   if (!hasPermission && permissionChecked && unauthorizedRedirectPath) {
    //     router.replace(unauthorizedRedirectPath); // Use replace to avoid back button issues
    //   }
    // }, [hasPermission, permissionChecked, router, unauthorizedRedirectPath]);
    // And then here you'd return null or a simple loading spinner temporarily.
    return <Forbidden />
  }

  // 3. If permission has been checked and granted, render the children
  return <>{children}</>
}

export default Guard
