'use client'
import { createContext, useContext, useEffect, useState } from 'react'
import { useUser } from '@clerk/nextjs'
import { createMongoAbility, type AnyAbility } from '@casl/ability'
import { Permit, permitState } from 'permit-fe-sdk'

export const AbilityContext = createContext<AnyAbility | undefined>(undefined)

export const useAbilityContext = () => {
  return useContext(AbilityContext)
}

export const AbilityLoader = ({ children }: { children: React.ReactNode }) => {
  const { isSignedIn, user } = useUser()
  const [ability, setAbility] = useState<AnyAbility | undefined>(undefined)

  useEffect(() => {
    const getAbility = async (loggedInUser: string) => {
      const permit = Permit({
        loggedInUser: loggedInUser,
        backendUrl: '/api/permit-io',
      })

      await permit.loadLocalStateBulk([
        // Permission for layout
        { action: 'view', resource: 'admin_area' },
        { action: 'view', resource: 'instructor_area' },
        // Permission for course
        { action: 'read', resource: 'course' },
        { action: 'create', resource: 'course' },
        { action: 'delete', resource: 'course' },
        { action: 'publish', resource: 'course' },
        { action: 'review', resource: 'course' },
        { action: 'update', resource: 'course' },
        // Permission for page
        { action: 'view', resource: 'review_page' },
        { action: 'view', resource: 'dashboard_page' },
        { action: 'view', resource: 'course_management_page' },
        { action: 'view', resource: 'performance_page' },
      ])
      const caslConfig = permitState.getCaslJson()

      return caslConfig?.length ? createMongoAbility(caslConfig) : undefined
    }

    if (isSignedIn) {
      getAbility(user.publicMetadata.db_user_id as string).then((caslAbility) => {
        setAbility(caslAbility)
      })
    }
  }, [isSignedIn, user])

  return <AbilityContext.Provider value={ability}>{children}</AbilityContext.Provider>
}
